import React, { useState, useEffect, useMemo, useRef } from 'react'
import {
  FolderIcon,
  FolderOpenIcon,
  DocumentIcon,
  HtmlIcon,
  FolderPlusIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  EditIcon,
  DeleteIcon
} from '../Icons/IconLibrary'
import { useDirectoryStore } from '../../stores/directoryStore'
import { DirectoryItem } from '../../services/directoryService'
import ContextMenu, {
  createDirectoryContextMenuItems,
  createFolderContextMenuItems,
  createNoteContextMenuItems,
  createHtmlContextMenuItems
} from './ContextMenu'
import DirectoryTabs from './DirectoryTabs'
import TrashBin from './TrashBin'

// 树项组件属性
interface TreeItemProps {
  item: DirectoryItem
  level: number
  isSelected: boolean
  onSelect: (id: string) => void
  onToggle: (id: string) => void
  onRename: (id: string, newName: string) => void
  onDelete: (id: string) => void
  onItemContextMenu?: (e: React.MouseEvent, item: DirectoryItem) => void
  expandedNodes: Set<string>
  searchQuery?: string
  isNewlyCreated?: boolean
  onCancelCreate?: (id: string) => void
}

// 组件属性
interface DirectoryTreeProps {
  onSelectNote: (noteId: string) => void
  selectedNoteId?: string
  searchQuery?: string
}

// 树项组件
const TreeItem: React.FC<TreeItemProps & { newlyCreatedItems?: Set<string> }> = ({
  item,
  level,
  isSelected,
  onSelect,
  onToggle,
  onRename,
  onDelete,
  onItemContextMenu,
  expandedNodes,
  searchQuery = '',
  isNewlyCreated = false,
  onCancelCreate,
  newlyCreatedItems = new Set()
}) => {
  const [isEditing, setIsEditing] = useState(isNewlyCreated)
  const [newName, setNewName] = useState(item.name)
  const [nameError, setNameError] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const isExpanded = expandedNodes.has(item._id)
  const hasChildren = item.children && item.children.length > 0

  // 验证文件名
  const validateName = (name: string): string => {
    if (!name.trim()) {
      return '名称不能为空'
    }

    // 检查非法字符
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(name)) {
      return '名称不能包含以下字符: < > : " / \\ | ? *'
    }

    // 检查长度
    if (name.length > 255) {
      return '名称长度不能超过255个字符'
    }

    return ''
  }

  // 处理重命名
  const handleRename = () => {
    const error = validateName(newName)
    if (error) {
      setNameError(error)
      return
    }

    setNameError('')

    if (newName.trim() && newName !== item.name) {
      onRename(item._id, newName)
    }
    setIsEditing(false)
  }

  // 处理取消编辑
  const handleCancel = () => {
    if (isNewlyCreated && onCancelCreate) {
      // 如果是新创建的项目，删除它
      onCancelCreate(item._id)
    } else {
      // 如果是重命名，恢复原名称
      setNewName(item.name)
      setIsEditing(false)
      setNameError('')
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  // 处理新创建项目的自动聚焦和文本选择
  useEffect(() => {
    if (isNewlyCreated && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isNewlyCreated])

  // 处理名称变化时清除错误
  useEffect(() => {
    if (nameError && newName.trim()) {
      setNameError('')
    }
  }, [newName, nameError])

  // 处理点击
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (item.type === 'folder') {
      onToggle(item._id)
    } else {
      onSelect(item._id)
    }
  }

  // 高亮搜索匹配文本
  const highlightMatch = (text: string) => {
    if (!searchQuery) return text

    const parts = text.split(new RegExp(`(${searchQuery})`, 'gi'))
    return (
      <>
        {parts.map((part, i) =>
          part.toLowerCase() === searchQuery.toLowerCase() ? (
            <span key={i} className="bg-theme-warning/30 text-theme-text">{part}</span>
          ) : (
            part
          )
        )}
      </>
    )
  }

  // 获取图标
  const getIcon = () => {
    if (item.type === 'folder') {
      return isExpanded ? FolderOpenIcon : FolderIcon
    } else if (item.type === 'html') {
      return HtmlIcon
    } else {
      return DocumentIcon
    }
  }

  const Icon = getIcon()

  // 计算缩进
  const indent = level * 16

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  return (
    <>
      <div
        className={`
          relative flex items-center py-1 px-2 rounded-md cursor-pointer group
          transition-all duration-200 ease-out
          ${isSelected ? 'bg-theme-primary/10 text-theme-primary' : 'hover:bg-theme-surface-hover'}
        `}
        style={{ paddingLeft: `${indent + 8}px` }}
        onClick={handleClick}
        onContextMenu={onItemContextMenu ?
          (e) => onItemContextMenu(e, item) : undefined}
      >
        {/* 展开/折叠图标 */}
        {item.type === 'folder' && (
          <div
            className="w-4 h-4 flex items-center justify-center mr-1"
            onClick={(e) => {
              e.stopPropagation()
              onToggle(item._id)
            }}
          >
            {hasChildren && (
              isExpanded ? (
                <ChevronDownIcon className="icon-xs text-theme-text-muted" />
              ) : (
                <ChevronRightIcon className="icon-xs text-theme-text-muted" />
              )
            )}
          </div>
        )}

        {/* 图标 */}
        <Icon className={`
          icon-sm mr-2 flex-shrink-0
          ${item.type === 'folder'
            ? 'text-theme-folder'
            : item.type === 'html'
              ? 'text-theme-html'
              : 'text-theme-markdown'
          }
        `} />

        {/* 名称 */}
        {isEditing ? (
          <div className="flex-1">
            <input
              ref={inputRef}
              type="text"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyDown}
              className={`
                w-full bg-theme-bg border rounded px-1 py-0.5 text-sm focus:outline-none
                ${nameError
                  ? 'border-theme-danger focus:border-theme-danger'
                  : 'border-theme-primary focus:border-theme-primary'
                }
              `}
              onClick={(e) => e.stopPropagation()}
              placeholder={item.type === 'folder' ? '文件夹名称' : '文件名称'}
            />
            {nameError && (
              <div className="text-xs text-theme-danger mt-1 px-1">
                {nameError}
              </div>
            )}
          </div>
        ) : (
          <span className="text-sm truncate flex-1">
            {highlightMatch(item.name)}
          </span>
        )}

        {/* 操作按钮 */}
        {!isEditing && (
          <div className="flex items-center opacity-0 group-hover:opacity-100">
            <button
              className="p-1 text-theme-text-muted hover:text-theme-text"
              onClick={(e) => {
                e.stopPropagation()
                setIsEditing(true)
              }}
              title="重命名"
            >
              <EditIcon className="icon-xs" />
            </button>
            <button
              className="p-1 text-theme-text-muted hover:text-theme-danger"
              onClick={(e) => {
                e.stopPropagation()
                onDelete(item._id)
              }}
              title="删除"
            >
              <DeleteIcon className="icon-xs" />
            </button>
          </div>
        )}
      </div>

      {/* 子节点 */}
      {isExpanded && hasChildren && (
        <div>
          {item.children!.map((child) => (
            <TreeItem
              key={child._id}
              item={child}
              level={level + 1}
              isSelected={isSelected}
              onSelect={onSelect}
              onToggle={onToggle}
              onRename={onRename}
              onDelete={onDelete}
              onItemContextMenu={onItemContextMenu}
              expandedNodes={expandedNodes}
              searchQuery={searchQuery}
              isNewlyCreated={newlyCreatedItems.has(child._id)}
              newlyCreatedItems={newlyCreatedItems}
              onCancelCreate={onCancelCreate}
            />
          ))}
        </div>
      )}
    </>
  )
}

// 主目录树组件
const DirectoryTree: React.FC<DirectoryTreeProps> = ({
  onSelectNote,
  selectedNoteId,
  searchQuery = ''
}) => {
  const {
    items,
    loading,
    error,
    expandedNodes,
    recentFiles,
    sharedFiles,
    allFiles,
    loadDirectoryTree,
    createItem,
    updateItem,
    deleteItem,
    toggleExpanded,
    setSelectedItem,
    setError
  } = useDirectoryStore()

  // 视图模式状态
  const [activeTab, setActiveTab] = useState<'all' | 'recent' | 'shared'>('all')

  // 新创建项目的状态跟踪
  const [newlyCreatedItems, setNewlyCreatedItems] = useState<Set<string>>(new Set())

  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState<{
    isVisible: boolean
    position: { x: number; y: number }
    targetItem: DirectoryItem | null
    targetType: 'empty' | 'folder' | 'note' | 'html'
  }>({
    isVisible: false,
    position: { x: 0, y: 0 },
    targetItem: null,
    targetType: 'empty'
  })

  const treeContainerRef = useRef<HTMLDivElement>(null)

  // 加载目录树数据
  useEffect(() => {
    loadDirectoryTree()
  }, [])

  // 过滤树数据（搜索功能）
  const filteredTreeData = useMemo(() => {
    if (!searchQuery) return items

    // 递归搜索
    const searchInNode = (node: DirectoryItem): DirectoryItem | null => {
      // 检查当前节点是否匹配
      if (node.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return { ...node, children: [] }
      }

      // 检查子节点
      if (node.children && node.children.length > 0) {
        const matchedChildren = node.children
          .map(searchInNode)
          .filter((child): child is DirectoryItem => child !== null)

        if (matchedChildren.length > 0) {
          return { ...node, children: matchedChildren }
        }
      }

      return null
    }

    return items
      .map(searchInNode)
      .filter((node): node is DirectoryItem => node !== null)
  }, [items, searchQuery])

  // 当前搜索查询
  const currentSearchQuery = searchQuery?.trim() || ''

  // 计算各个标签的文件数量
  const allFilesCount = useMemo(() => {
    return allFiles.length
  }, [allFiles])

  const recentFilesCount = useMemo(() => {
    return recentFiles.length
  }, [recentFiles])

  const sharedFilesCount = useMemo(() => {
    return sharedFiles.length
  }, [sharedFiles])

  // 处理选择节点
  const handleSelect = (id: string) => {
    setSelectedItem(id)
    onSelectNote(id)
  }

  // 处理展开/折叠节点
  const handleToggle = (id: string) => {
    toggleExpanded(id)
  }

  // 处理重命名
  const handleRename = async (id: string, newName: string) => {
    try {
      await updateItem(id, { name: newName })

      // 如果是新创建的项目，重命名完成后移除标记
      if (newlyCreatedItems.has(id)) {
        setNewlyCreatedItems(prev => {
          const newSet = new Set(prev)
          newSet.delete(id)
          return newSet
        })

        // 如果是文件，自动选中
        const item = findItemById(id, items)
        if (item && item.type !== 'folder') {
          handleSelect(id)
        }
      }
    } catch (error) {
      console.error('Failed to rename item:', error)
    }
  }

  // 辅助函数：根据ID查找项目
  const findItemById = (id: string, itemList: DirectoryItem[]): DirectoryItem | null => {
    for (const item of itemList) {
      if (item._id === id) {
        return item
      }
      if (item.children) {
        const found = findItemById(id, item.children)
        if (found) return found
      }
    }
    return null
  }

  // 处理删除（移到回收站）
  const handleDelete = async (id: string) => {
    if (window.confirm('确定要将此项目移到回收站吗？')) {
      try {
        await deleteItem(id)
      } catch (error) {
        console.error('Failed to delete item:', error)
      }
    }
  }

  // 处理创建子项
  const handleCreateChild = async (parentId: string, type: 'folder' | 'note' | 'html') => {
    try {
      let name = ''
      let content = ''

      if (type === 'folder') {
        name = '新建文件夹'
      } else if (type === 'note') {
        name = '新建笔记.md'
        content = '# 新建笔记\n\n开始编写您的内容...'
      } else if (type === 'html') {
        name = '新建文件.html'
        content = '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>新建HTML文件</title>\n</head>\n<body>\n    <h1>Hello World</h1>\n    <p>开始编写您的HTML内容...</p>\n</body>\n</html>'
      }

      const newItem = await createItem({
        name,
        type,
        parentId: parentId === 'root' ? 'root' : parentId,
        content
      })

      // 标记为新创建的项目，以便进入编辑模式
      setNewlyCreatedItems(prev => new Set(prev).add(newItem._id))

      // 确保父文件夹展开（如果不是根目录）
      if (parentId !== 'root') {
        toggleExpanded(parentId)
      }

      // 关闭右键菜单
      closeContextMenu()

    } catch (error) {
      console.error('Failed to create item:', error)
    }
  }

  // 处理取消创建（删除新创建的项目）
  const handleCancelCreate = async (id: string) => {
    try {
      await deleteItem(id)
      setNewlyCreatedItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    } catch (error) {
      console.error('Failed to cancel create:', error)
    }
  }
  
  // 处理右键菜单（空白区域）
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()

    // 确保点击在目录树区域内
    if (treeContainerRef.current && treeContainerRef.current.contains(e.target as Node)) {
      setContextMenu({
        isVisible: true,
        position: { x: e.clientX, y: e.clientY },
        targetItem: null,
        targetType: 'empty'
      })
    }
  }

  // 处理项目右键菜单
  const handleItemContextMenu = (e: React.MouseEvent, item: DirectoryItem) => {
    e.preventDefault()
    e.stopPropagation()

    let targetType: 'folder' | 'note' | 'html' = 'folder'
    if (item.type === 'note') targetType = 'note'
    else if (item.type === 'html') targetType = 'html'

    setContextMenu({
      isVisible: true,
      position: { x: e.clientX, y: e.clientY },
      targetItem: item,
      targetType
    })
  }
  
  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu({
      ...contextMenu,
      isVisible: false,
      targetItem: null,
      targetType: 'empty'
    })
  }
  
  // 创建右键菜单项
  const contextMenuItems = useMemo(() => {
    const targetItem = contextMenu.targetItem
    const targetType = contextMenu.targetType

    // 空白区域右键菜单
    if (targetType === 'empty') {
      return createDirectoryContextMenuItems(
        () => handleCreateChild('root', 'folder'),
        () => handleCreateChild('root', 'note'),
        () => handleCreateChild('root', 'html')
      )
    }

    // 文件夹右键菜单
    if (targetType === 'folder' && targetItem) {
      return createFolderContextMenuItems(
        () => handleToggle(targetItem._id), // 打开/展开文件夹
        () => handleRename(targetItem._id, targetItem.name), // 重命名
        () => handleCreateChild(targetItem._id, 'folder'), // 新建文件夹
        () => handleCreateChild(targetItem._id, 'note'), // 新建笔记
        () => handleCreateChild(targetItem._id, 'html'), // 新建HTML
        () => console.log('复制文件夹:', targetItem.name), // 复制
        () => console.log('移动文件夹:', targetItem.name), // 移动
        () => console.log('分享文件夹:', targetItem.name), // 分享
        () => handleDelete(targetItem._id) // 删除
      )
    }

    // Markdown文件右键菜单
    if (targetType === 'note' && targetItem) {
      return createNoteContextMenuItems(
        () => handleSelect(targetItem._id), // 打开
        () => handleSelect(targetItem._id), // 编辑
        () => console.log('预览笔记:', targetItem.name), // 预览
        () => handleRename(targetItem._id, targetItem.name), // 重命名
        () => console.log('复制笔记:', targetItem.name), // 复制
        () => console.log('移动笔记:', targetItem.name), // 移动
        () => console.log('下载笔记:', targetItem.name), // 下载
        () => console.log('分享笔记:', targetItem.name), // 分享
        () => handleDelete(targetItem._id) // 删除
      )
    }

    // HTML文件右键菜单
    if (targetType === 'html' && targetItem) {
      return createHtmlContextMenuItems(
        () => handleSelect(targetItem._id), // 打开
        () => handleSelect(targetItem._id), // 编辑
        () => console.log('预览HTML:', targetItem.name), // 预览
        () => handleRename(targetItem._id, targetItem.name), // 重命名
        () => console.log('复制HTML:', targetItem.name), // 复制
        () => console.log('移动HTML:', targetItem.name), // 移动
        () => console.log('下载HTML:', targetItem.name), // 下载
        () => console.log('分享HTML:', targetItem.name), // 分享
        () => handleDelete(targetItem._id) // 删除
      )
    }

    // 默认返回空数组
    return []
  }, [contextMenu.targetType, contextMenu.targetItem])

  // 标签配置
  const tabs = [
    {
      id: 'all' as const,
      label: '全部',
      count: allFilesCount
    },
    {
      id: 'recent' as const,
      label: '最近',
      count: recentFilesCount
    },
    {
      id: 'shared' as const,
      label: '分享',
      count: sharedFilesCount
    }
  ]

  return (
    <div className="h-full flex flex-col">
      {/* 标签菜单栏 */}
      <div className="flex items-center bg-theme-surface/30 border-b border-theme-border/30">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id

          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium
                transition-all duration-200 ease-out flex-1 min-w-0
                ${isActive
                  ? 'text-theme-text border-b-2 border-theme-primary bg-theme-primary/5'
                  : 'text-theme-text-muted hover:text-theme-text hover:bg-theme-surface-hover/50'
                }
              `}
            >
              <span className="truncate">{tab.label}</span>
              {tab.count > 0 && (
                <span className={`
                  text-xs px-1.5 py-0.5 rounded-full flex-shrink-0
                  ${isActive
                    ? 'bg-theme-primary/20 text-theme-primary'
                    : 'bg-theme-text-muted/20 text-theme-text-muted'
                  }
                `}>
                  {tab.count}
                </span>
              )}
            </button>
          )
        })}
      </div>

      {/* 内容区域 */}
      {activeTab === 'recent' || activeTab === 'shared' ? (
        <DirectoryTabs
          activeTab={activeTab}
          onSelectNote={onSelectNote}
          selectedNoteId={selectedNoteId}
        />
      ) : (
        <div
          ref={treeContainerRef}
          className="flex-1 overflow-auto"
          onContextMenu={handleContextMenu}
        >
          <div className="p-3">
            {/* 搜索结果提示 */}
            {currentSearchQuery && (
              <div className="px-2 py-1 mb-2 text-xs text-theme-text-muted">
                搜索 "{currentSearchQuery}" 的结果 ({filteredTreeData.length} 项)
              </div>
            )}

            {/* 加载状态 */}
            {loading && (
              <div className="px-2 py-8 text-center">
                <div className="w-8 h-8 border-2 border-theme-primary border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                <p className="text-sm text-theme-text-muted">加载中...</p>
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="px-2 py-4 text-center">
                <p className="text-sm text-theme-danger mb-2">{error}</p>
                <button
                  onClick={() => {
                    setError(null)
                    loadDirectoryTree()
                  }}
                  className="text-xs text-theme-primary hover:underline"
                >
                  重试
                </button>
              </div>
            )}

            {/* 目录树 */}
            {!loading && !error && (
              <div className="space-y-0.5">
                {filteredTreeData.length > 0 ? (
                  filteredTreeData.map((item) => (
                    <TreeItem
                      key={item._id}
                      item={item}
                      level={0}
                      isSelected={selectedNoteId === item._id}
                      onSelect={handleSelect}
                      onToggle={handleToggle}
                      onRename={handleRename}
                      onDelete={handleDelete}
                      onItemContextMenu={handleItemContextMenu}
                      expandedNodes={expandedNodes}
                      searchQuery={currentSearchQuery}
                      isNewlyCreated={newlyCreatedItems.has(item._id)}
                      onCancelCreate={handleCancelCreate}
                      newlyCreatedItems={newlyCreatedItems}
                    />
                  ))
                ) : currentSearchQuery ? (
                /* 搜索无结果 */
                <div className="px-2 py-8 text-center">
                  <div className="w-12 h-12 bg-theme-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <DocumentIcon className="icon-xl text-theme-primary/50" />
                  </div>
                  <h3 className="text-sm font-theme-medium text-theme-text mb-1">
                    未找到匹配的笔记
                  </h3>
                  <p className="text-xs text-theme-text-muted">
                    尝试使用不同的关键词搜索
                  </p>
                </div>
              ) : (
                /* 空状态 */
                <div className="px-2 py-8 text-center">
                  <div className="w-12 h-12 bg-theme-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <FolderPlusIcon className="icon-xl text-theme-primary/50" />
                  </div>
                  <h3 className="text-sm font-theme-medium text-theme-text mb-1">
                    还没有笔记
                  </h3>
                  <p className="text-xs text-theme-text-muted">
                    右键点击空白区域创建您的第一个笔记
                  </p>
                </div>
              )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 回收站 */}
      <div className="mt-4 border-t border-theme-border pt-4">
        <TrashBin />
      </div>

      {/* 右键菜单 */}
      <ContextMenu
        isVisible={contextMenu.isVisible}
        position={contextMenu.position}
        items={contextMenuItems}
        onClose={closeContextMenu}
      />
    </div>
  )
}

export default DirectoryTree
