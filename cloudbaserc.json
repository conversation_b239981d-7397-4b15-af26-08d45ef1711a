{"envId": "ai-demo-8gjoyg63e237ce06", "version": "2.0", "framework": {"name": "react", "plugins": {"client": {"use": "@cloudbase/framework-plugin-website", "inputs": {"buildCommand": "npm run build", "outputPath": "dist", "cloudPath": "/cloud-notes", "ignore": [".git", ".github", "node_modules", "src"]}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "auth", "timeout": 30, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main"}, {"name": "notes", "timeout": 30, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main"}]}}}}, "requirement": {"addons": [{"type": "CloudBase", "version": "^1.0.0"}], "environment": {"loginRequired": false, "installDeps": true}}, "hooks": {"preDeploy": {"type": "execCommand", "commands": ["npm install"]}, "postDeploy": {"type": "callFunction", "functions": []}}, "database": {"collections": [{"collectionName": "users", "description": "用户信息集合", "aclTag": "ADMINONLY"}, {"collectionName": "notes", "description": "笔记内容集合", "aclTag": "PRIVATE"}, {"collectionName": "folders", "description": "文件夹结构集合", "aclTag": "PRIVATE"}, {"collectionName": "shares", "description": "分享配置集合", "aclTag": "PRIVATE"}]}, "hosting": {"history": {"rewrites": [{"source": "**", "destination": "/solar-notes/index.html"}]}}}